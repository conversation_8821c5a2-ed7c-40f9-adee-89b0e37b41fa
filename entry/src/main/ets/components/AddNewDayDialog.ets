/**
 * 添加新的一天弹窗组件
 * 用于添加新的一天到行程中
 */

import { THEME_COLORS } from '../utils/TripUtils';

// 添加新的一天表单数据接口
export interface AddNewDayFormData {
  dayTitle: string;
  date: string;
  description: string;
  insertPosition: 'end' | 'after_current' | 'after_specific';
}

@Component
export struct AddNewDayDialog {
  @Prop isVisible: boolean = false;
  @State formData: AddNewDayFormData = {
    dayTitle: '',
    date: '',
    description: '',
    insertPosition: 'end'
  };
  @State dayNumber: number = 1;
  @State suggestedDate: string = '';
  @State showDatePicker: boolean = false;

  onConfirm?: (formData: AddNewDayFormData) => void;
  onCancel?: () => void;

  aboutToAppear() {
    // 设置默认标题和日期
    this.formData.dayTitle = `第${this.dayNumber}天`;
    this.formData.date = this.suggestedDate;
  }

  // 格式化日期显示
  formatDateDisplay(dateStr: string): string {
    if (!dateStr) return '';
    return dateStr.replace(/-/g, '/');
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.dayTitle.trim()) {
      console.error('请输入日程标题');
      return false;
    }
    if (!this.formData.date) {
      console.error('请选择日期');
      return false;
    }
    return true;
  }

  // 处理确认
  handleConfirm = () => {
    if (!this.validateForm()) {
      return;
    }
    
    if (this.onConfirm) {
      this.onConfirm(this.formData);
    }
  }

  // 处理取消
  handleCancel = () => {
    if (this.onCancel) {
      this.onCancel();
    }
  }

  @Builder
  buildDialogContent() {
    Column() {
      // 标题
      Text('添加新的一天')
        .fontSize(18)
        .fontWeight(600)
        .fontColor(THEME_COLORS.textPrimary)
        .margin({ bottom: 20 })

      // 日程标题输入
      Column() {
        Row() {
          Text('日程标题')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 2 })
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
        .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入日程标题', text: this.formData.dayTitle })
          .width('100%')
          .height(44)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12 })
          .fontSize(14)
          .onChange((value: string) => {
            this.formData.dayTitle = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 日期选择
      Column() {
        Row() {
          Text('日期')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 2 })
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
        .margin({ bottom: 8 })

        Button() {
          Row() {
            Text(this.formData.date ? this.formatDateDisplay(this.formData.date) : '请选择日期')
              .fontSize(14)
              .fontColor(this.formData.date ? THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
              .layoutWeight(1)
              .textAlign(TextAlign.Start)
            
            Text('📅')
              .fontSize(16)
          }
          .width('100%')
          .height(44)
          .padding({ left: 12, right: 12 })
          .alignItems(VerticalAlign.Center)
        }
        .width('100%')
        .height(44)
        .backgroundColor('#F5F5F5')
        .borderRadius(8)
        .type(ButtonType.Normal)
        .stateEffect(false)
        .onClick(() => {
          this.showDatePicker = true;
        })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 天数序号显示
      Column() {
        Text('天数序号')
          .fontSize(14)
          .fontColor(THEME_COLORS.textPrimary)
          .fontWeight(500)
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ bottom: 8 })

        Text(`${this.dayNumber}`)
          .fontSize(14)
          .fontColor(THEME_COLORS.textSecondary)
          .width('100%')
          .height(44)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12 })
          .textAlign(TextAlign.Start)
          .border({ width: 0 })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 日程描述
      Column() {
        Text('日程描述')
          .fontSize(14)
          .fontColor(THEME_COLORS.textPrimary)
          .fontWeight(500)
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ bottom: 8 })

        TextArea({ placeholder: '请输入日程描述（可选）', text: this.formData.description })
          .width('100%')
          .height(80)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .fontSize(14)
          .onChange((value: string) => {
            this.formData.description = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 24 })

      // 插入位置选择
      Column() {
        Text('插入位置')
          .fontSize(14)
          .fontColor(THEME_COLORS.textPrimary)
          .fontWeight(500)
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ bottom: 12 })

        Column() {
          // 添加到最后
          Row() {
            Radio({ value: 'end', group: 'insertPosition' })
              .checked(this.formData.insertPosition === 'end')
              .onChange((isChecked: boolean) => {
                if (isChecked) {
                  this.formData.insertPosition = 'end';
                }
              })
              .margin({ right: 8 })

            Text('添加到最后')
              .fontSize(14)
              .fontColor(THEME_COLORS.textPrimary)
              .layoutWeight(1)
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)
          .margin({ bottom: 8 })

          // 插入到指定位置（暂时隐藏，简化实现）
          // Row() {
          //   Radio({ value: 'after_current', group: 'insertPosition' })
          //     .checked(this.formData.insertPosition === 'after_current')
          //     .onChange((isChecked: boolean) => {
          //       if (isChecked) {
          //         this.formData.insertPosition = 'after_current';
          //       }
          //     })
          //     .margin({ right: 8 })

          //   Text('插入到"抵达与探索"之后')
          //     .fontSize(14)
          //     .fontColor(THEME_COLORS.textPrimary)
          //     .layoutWeight(1)
          // }
          // .width('100%')
          // .alignItems(VerticalAlign.Center)
          // .margin({ bottom: 8 })

          // Row() {
          //   Radio({ value: 'after_specific', group: 'insertPosition' })
          //     .checked(this.formData.insertPosition === 'after_specific')
          //     .onChange((isChecked: boolean) => {
          //       if (isChecked) {
          //         this.formData.insertPosition = 'after_specific';
          //       }
          //     })
          //     .margin({ right: 8 })

          //   Text('插入到"经典景点"之后')
          //     .fontSize(14)
          //     .fontColor(THEME_COLORS.textPrimary)
          //     .layoutWeight(1)
          // }
          // .width('100%')
          // .alignItems(VerticalAlign.Center)
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 24 })

      // 按钮区域
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor(THEME_COLORS.textSecondary)
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: '#E0E0E0' })
          .borderRadius(8)
          .layoutWeight(1)
          .height(44)
          .onClick(this.handleCancel)

        Button('+ 添加日程')
          .fontSize(16)
          .fontColor(Color.White)
          .backgroundColor(THEME_COLORS.primary)
          .borderRadius(8)
          .layoutWeight(1)
          .height(44)
          .margin({ left: 12 })
          .onClick(this.handleConfirm)
      }
      .width('100%')
    }
    .width('100%')
    .padding(20)
    .backgroundColor(Color.White)
    .borderRadius(16)
  }

  build() {
    if (this.isVisible) {
      Stack() {
        // 背景遮罩
        Column()
          .width('100%')
          .height('100%')
          .backgroundColor('rgba(0, 0, 0, 0.5)')
          .onClick(this.handleCancel)

        // 弹窗内容
        Column() {
          this.buildDialogContent()
        }
        .width('calc(100% - 32vp)')
        .maxWidth(400)
        .justifyContent(FlexAlign.Center)

        // 日期选择器
        if (this.showDatePicker) {
          Column() {
            Text('选择日期')
              .fontSize(16)
              .fontWeight(600)
              .fontColor(THEME_COLORS.textPrimary)
              .margin({ bottom: 16 })

            DatePicker({
              start: new Date('2024-01-01'),
              end: new Date('2025-12-31'),
              selected: this.formData.date ? new Date(this.formData.date) : new Date()
            })
              .onChange((value: DatePickerResult) => {
                const year = value.year;
                const month = String(value.month + 1).padStart(2, '0');
                const day = String(value.day).padStart(2, '0');
                this.formData.date = `${year}-${month}-${day}`;
              })

            Row() {
              Button('取消')
                .fontSize(14)
                .fontColor(THEME_COLORS.textSecondary)
                .backgroundColor(Color.Transparent)
                .border({ width: 1, color: '#E0E0E0' })
                .borderRadius(6)
                .layoutWeight(1)
                .height(36)
                .onClick(() => {
                  this.showDatePicker = false;
                })

              Button('确定')
                .fontSize(14)
                .fontColor(Color.White)
                .backgroundColor(THEME_COLORS.primary)
                .borderRadius(6)
                .layoutWeight(1)
                .height(36)
                .margin({ left: 8 })
                .onClick(() => {
                  this.showDatePicker = false;
                })
            }
            .width('100%')
            .margin({ top: 16 })
          }
          .width('calc(100% - 64vp)')
          .maxWidth(320)
          .padding(20)
          .backgroundColor(Color.White)
          .borderRadius(12)
          .shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetY: 4 })
        }
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    }
  }
}
